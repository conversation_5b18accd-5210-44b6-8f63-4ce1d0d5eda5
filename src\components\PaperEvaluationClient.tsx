'use client';

import Link from 'next/link';
import { useState, useEffect } from 'react';
import PaperEvaluation from '@/components/PaperEvaluation';
import PaperEvaluationV3 from '@/components/PaperEvaluationV3';
import { useModelContext } from '@/context/ModelContext';
import PaperNavigation from '@/components/PaperNavigation';
import GlossaryText from '@/components/GlossaryText';



interface PaperEvaluationClientProps {
  paperId: string;
}

export default function PaperEvaluationClient({ paperId }: PaperEvaluationClientProps) {
  const [evaluationJson, setEvaluationJson] = useState<any | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showRawMarkdown, setShowRawMarkdown] = useState(false);

  // Get the selected model from context
  const { selectedModel } = useModelContext();

  // Convert paperId to proper filename format
  // Example: 'adaptive_temperature_sampling' -> 'Adaptive_Temperature_Sampling'
  const evaluationFile = paperId
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join('_');

  useEffect(() => {
    // Fetch the JSON evaluation file to get paper metadata using the new folder structure and model-specific files
    const jsonPath = `/papers/evaluations/${evaluationFile}/Evaluation_${evaluationFile}_${selectedModel}.json`;
    console.log('Loading JSON from:', jsonPath);

    fetch(jsonPath)
      .then(response => {
        if (!response.ok) {
          throw new Error(`Failed to load evaluation JSON for ${paperId}`);
        }
        return response.json();
      })
      .then(data => {
        setEvaluationJson(data);
        setLoading(false);
        setError(null);
      })
      .catch(err => {
        console.error('Error loading JSON evaluation:', err);
        setError('Failed to load paper evaluation');
        setLoading(false);
      });
  }, [paperId, evaluationFile, selectedModel]);

  // Determine if the loaded JSON is V3
  const isV3 = evaluationJson && evaluationJson.executive_summary_for_audio_dev;
  // console.log(`PaperEvaluationClient: evaluationFile: ${evaluationFile}, isV3: ${isV3}`); // For debugging

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center">
          <h1 className="text-4xl font-bold mb-4 text-gray-900 dark:text-gray-100">
            <GlossaryText
              text="Loading..."
              className="text-4xl font-bold text-gray-900 dark:text-gray-100"
            />
          </h1>
          <p className="text-gray-800 dark:text-gray-300 mb-8">
            <GlossaryText
              text="Loading paper evaluation data."
              className="text-gray-800 dark:text-gray-300"
            />
          </p>
        </div>
      </div>
    );
  }

  if (error || !evaluationJson) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center">
          <h1 className="text-4xl font-bold mb-4 text-gray-900 dark:text-gray-100">
            <GlossaryText
              text="Paper Not Found"
              className="text-4xl font-bold text-gray-900 dark:text-gray-100"
            />
          </h1>
          <p className="text-gray-800 dark:text-gray-300 mb-8">
            <GlossaryText
              text={error || 'The requested paper could not be found.'}
              className="text-gray-800 dark:text-gray-300"
            />
          </p>
          <Link
            href="/critical-review/source-material"
            className="text-blue-700 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300"
          >
            <GlossaryText
              text="← Back to Source Material"
              className="text-blue-700 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300"
            />
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-zinc-900 font-roboto">
      {/* Paper Navigation */}
      <PaperNavigation paperSlug={paperId} paperTitle={evaluationJson?.metadata?.title || 'Loading title...'} />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Navigation */}
        <div className="mb-8 flex justify-between items-center">
          <Link
            href="/critical-review/source-material"
            className="text-blue-700 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 flex items-center"
          >
            <GlossaryText
              text="← Back to Source Material"
              className="text-blue-700 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300"
            />
          </Link>
          <Link
            href={`/critical-review/source-material/${paperId}`}
            className="text-blue-700 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300"
          >
            <GlossaryText
              text="View Full Paper →"
              className="text-blue-700 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300"
            />
          </Link>
        </div>

        {/* Title Section */}
        <div className="bg-white dark:bg-zinc-900 rounded-lg shadow-sm dark:shadow-zinc-800/30 mb-8 border border-gray-200 dark:border-gray-700 border-t-0 overflow-hidden">
          <div className="h-1 bg-gradient-to-r from-blue-500 to-purple-500"></div>
          <div className="p-6">
            <div className="flex justify-between items-end">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                  <GlossaryText
                    text={evaluationJson?.metadata?.title || 'Title Not Available'}
                    className="text-3xl font-bold text-gray-900 dark:text-gray-100"
                  />
                </h1>
                <p className="text-lg text-gray-600 dark:text-gray-400">
                  <GlossaryText
                    text={`${evaluationJson?.metadata?.authors || 'Authors Not Available'} (${evaluationJson?.metadata?.year || 'N/A'})`}
                    className="text-lg text-gray-600 dark:text-gray-400"
                  />
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-500 mt-2 mb-0">
                  <GlossaryText
                    text="Paper Evaluation Report"
                    className="text-sm text-gray-500 dark:text-gray-500"
                  />
                </p>
              </div>
              <div>
                <button
                  onClick={() => setShowRawMarkdown(!showRawMarkdown)}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:text-gray-400 dark:bg-zinc-900 dark:hover:bg-zinc-800 dark:border-gray-700 whitespace-nowrap min-w-[120px]"
                >
                  {showRawMarkdown ? 'Hide Raw JSON' : 'Show Raw JSON'}
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Evaluation Content */}
        <div className="dark:bg-zinc-900">
          {evaluationJson && (
            isV3 ? (
              <PaperEvaluationV3 evaluationData={evaluationJson} showRawMarkdown={showRawMarkdown} />
            ) : (
              <PaperEvaluation evaluationData={evaluationJson} showRawMarkdown={showRawMarkdown} />
            )
          )}
        </div>
      </div>
    </div>
  );
}
