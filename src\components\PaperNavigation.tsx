'use client';

import React from 'react';
import Link from 'next/link';
import GlossaryText from '@/components/GlossaryText';
import {
  DocumentTextIcon,
  ClipboardDocumentListIcon,
  ScaleIcon,
  CubeTransparentIcon,
  AcademicCapIcon,
  KeyIcon,
  ExclamationTriangleIcon,
  ChatBubbleLeftRightIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

interface PaperNavigationProps {
  paperSlug: string;
  paperTitle?: string;
}

interface NavLinkItem {
  name: string;
  href: string;
  icon: React.ElementType;
  isExternal?: boolean;
  customHandler?: (e: React.MouseEvent) => void;
  active?: boolean;
}

const titleToId = (title: string): string => {
  return title.toLowerCase().replace(/\s+/g, '-').replace(/[&:]/g, '');
};

const PaperNavigation: React.FC<PaperNavigationProps> = ({ paperSlug, paperTitle }) => {
  const displayTitle = paperTitle || paperSlug
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');

  const handleTocNavigation = (e: React.MouseEvent, sectionId: string) => {
    e.preventDefault();
    const element = document.getElementById(sectionId);
    if (element) {
      const navbarHeight = 80; // Estimated height of the fixed navbar + some padding
      const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
      const offsetPosition = elementPosition - navbarHeight;

      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      });
    }
    // Optionally, update URL hash: window.location.hash = sectionId;
  };

  const tocSections = [
    { name: "Executive Summary", fullTitle: "Executive Summary for Audio Developers", icon: ClipboardDocumentListIcon },
    { name: "Scores", fullTitle: "Quantitative Scores", icon: ScaleIcon },
    { name: "Pillar Analysis", fullTitle: "Implementation Readiness", icon: CubeTransparentIcon },
    { name: "Explanations", fullTitle: "Multi-Level Explanations", icon: AcademicCapIcon },
    { name: "Key Learnings", fullTitle: "Key Learnings for Audio Developers", icon: KeyIcon },
    { name: "Critical Assessment", fullTitle: "Critical Assessment and Limitations", icon: ExclamationTriangleIcon },
    { name: "Methodology", fullTitle: "Methodological Deep Dive & Adaptation", icon: CubeTransparentIcon },
    { name: "Research Impact", fullTitle: "Impact on My Research & Development", icon: ChatBubbleLeftRightIcon },
    { name: "Final Verdict", fullTitle: "Final Verdict for Audio Developers", icon: CheckCircleIcon },
  ];

  const navLinks: NavLinkItem[] = [
    {
      name: 'View Paper',
      href: `/critical-review/source-material/${paperSlug}`,
      icon: DocumentTextIcon,
      isExternal: true,
    },
    ...tocSections.map(section => ({
      name: section.name,
      href: `#${titleToId(section.fullTitle)}`,
      icon: section.icon,
      customHandler: (e: React.MouseEvent) => handleTocNavigation(e, titleToId(section.fullTitle)),
      active: false,
    }))
  ];

  return (
    <div className="fixed bottom-8 right-8 z-50">
      {/* Outer div for gradient border */}
      <div className="bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg p-0.5 shadow-lg">
        {/* Inner content div */}
        <div className="bg-white dark:bg-zinc-900 rounded-md overflow-hidden max-h-[70vh] flex flex-col shadow-sm dark:shadow-zinc-800/30">
          <div className="px-4 py-3 bg-gray-50 dark:bg-zinc-900 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-10">
            <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate max-w-[200px]">
              <GlossaryText
                text={displayTitle}
                className="text-sm font-medium text-gray-900 dark:text-gray-100"
              />
            </h3>
          </div>
          <nav className="flex flex-col overflow-y-auto flex-grow">
            {navLinks.map((link) => {
              return (
                <Link
                  key={link.name}
                  href={link.href}
                  onClick={link.customHandler}
                  target={link.isExternal ? "_blank" : "_self"}
                  rel={link.isExternal ? "noopener noreferrer" : undefined}
                  className={`flex items-center px-4 py-2 text-sm ${link.active
                      ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-400'
                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-zinc-700/50'
                    }`}
                >
                  <link.icon className="mr-3 h-5 w-5 flex-shrink-0" aria-hidden="true" />
                  <GlossaryText
                    text={link.name}
                    className="text-sm"
                  />
                </Link>
              );
            })}
          </nav>
        </div>
      </div>
    </div>
  );
};

export default PaperNavigation;
