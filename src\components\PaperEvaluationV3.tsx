'use client';

import React, { ReactNode, useRef, useState, useEffect } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import InlineGlossaryText from '@/components/InlineGlossaryText';


// Corrected Consolidated SOLID Icons Import from @heroicons/react/24/solid
import {
  AcademicCapIcon,
  ChatBubbleBottomCenterTextIcon,
  ChatBubbleLeftRightIcon as MainChatBubbleLeftRightIcon,
  CheckCircleIcon,
  CodeBracketSquareIcon,
  CommandLineIcon as MainCommandLineIcon,
  CubeTransparentIcon as MainCubeTransparentIcon,
  DocumentTextIcon as MainDocumentTextIcon,
  ExclamationTriangleIcon as MainExclamationTriangleIcon,
  KeyIcon,
  LightBulbIcon as MainLightBulbIcon,
  QuestionMarkCircleIcon as MainQuestionMarkCircleIcon,
  ScaleIcon as MainScaleIcon
} from '@heroicons/react/24/solid';

// Corrected Consolidated OUTLINE Icons Import from @heroicons/react/24/outline
import {
  AdjustmentsHorizontalIcon as SubAdjustmentsHorizontalIcon,
  ArrowPathIcon as SubArrowPathIcon,
  ArrowTrendingUpIcon as SubArrowTrendingUpIcon,
  BeakerIcon,
  BookOpenIcon as SubBookOpenIcon,
  ChartBarIcon as SubChartBarIcon,
  ChatBubbleLeftRightIcon as SubChatBubbleLeftRightIcon,
  ClipboardDocumentListIcon,
  CodeBracketIcon as SubCodeBracketIcon,
  CogIcon as SubCogIcon,
  CommandLineIcon as SubCommandLineIcon,
  ComputerDesktopIcon as SubComputerDesktopIcon,
  DocumentTextIcon as SubDocumentTextIcon,
  LightBulbIcon as SubLightBulbIcon,
  LinkIcon as SubLinkIcon,
  ListBulletIcon,
  MusicalNoteIcon as SubMusicalNoteIcon,
  PuzzlePieceIcon as SubPuzzlePieceIcon,
  QuestionMarkCircleIcon as SubQuestionMarkCircleIcon,
  ScaleIcon as SubScaleIcon,
  SparklesIcon as SubSparklesIcon,
  SpeakerWaveIcon as SubSpeakerWaveIcon,
  TableCellsIcon as SubTableCellsIcon
} from '@heroicons/react/24/outline';

// Helper to interpolate colors for gradient effect
const interpolateColor = (color1: string, color2: string, factor: number): string => {
  const r1 = parseInt(color1.substring(1, 3), 16);
  const g1 = parseInt(color1.substring(3, 5), 16);
  const b1 = parseInt(color1.substring(5, 7), 16);
  const r2 = parseInt(color2.substring(1, 3), 16);
  const g2 = parseInt(color2.substring(3, 5), 16);
  const b2 = parseInt(color2.substring(5, 7), 16);
  const r = Math.round(r1 + factor * (r2 - r1));
  const g = Math.round(g1 + factor * (g2 - g1));
  const b = Math.round(b1 + factor * (b2 - b1));
  return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
};

const GRADIENT_START_COLOR = '#3b82f6';
const GRADIENT_END_COLOR = '#a855f7';

const scoreMarkers = [20, 40, 60, 80]; // Positions for dotted lines

// Define the structure of the V3 evaluation data
interface EvaluationDataV3 {
  metadata: {
    title: string;
    authors: string;
    year: number;
    doi: string;
  };
  executive_summary_for_audio_dev: string;
  scores: {
    implementation_readiness: { [key: string]: number };
    verified_performance_impact: { [key: string]: number };
    problem_solving_novelty_insight: { [key: string]: number };
    audio_domain_translatability_impact: { [key: string]: number };
    total_weighted_score: number;
  };
  detailed_pillar_analysis?: {
    clarity_and_presentation?: PillarNarratives;
    methodology_and_execution?: PillarNarratives;
    innovation_and_originality?: PillarNarratives;
    relevance_and_impact?: PillarNarratives;
    implementation_readiness?: PillarNarratives;
    verified_performance_impact?: PillarNarratives;
    problem_solving_novelty_insight?: PillarNarratives;
    audio_domain_translatability_impact?: PillarNarratives;
    [key: string]: PillarNarratives | undefined;
  };
  multi_level_explanation: {
    level_1_musician_friend: string;
    level_2_juce_developer_no_ai_expert: string;
    level_3_music_tech_researcher: string;
    level_4_ai_specialist_condensed: string;
  };
  brainstormed_audio_applications: Array<{
    application_idea: string;
    brief_description: string;
  }>;
  key_learnings_for_audio_dev: string[];
  critical_assessment_and_limitations: string;
  juce_implementation_sketch: {
    description: string;
    code_sketch: string;
  };
  methodological_deep_dive_adaptation: Array<{
    methodName: string;
    simplifiedExplanationForAudioDev: string;
    prerequisites_for_audio_adaptation: string[];
    stepByStepAdaptationGuide_conceptual: string[];
    practicalAudioExample: {
      scenarioDescription: string;
      how_method_might_apply: string;
      expected_audio_outcome_if_successful: string;
    };
  }>;
  impact_on_my_research_and_development: {
    new_ideas_sparked: string;
    refinement_of_existing_ideas: string;
    potential_thesis_contribution_angle: string;
    questions_for_further_investigation: string[];
  };
  final_verdict_for_audio_dev: {
    is_worth_reading_thoroughly: string;
    primary_value_proposition: string;
    overall_suitability_score_for_my_needs: string;
    concluding_remarks: string;
  };
}

interface PillarNarratives {
  narrative_summary?: string;
  strengths?: string[];
  weaknesses?: string[];
  suggestions_for_improvement?: string[];
}

interface SectionCardProps {
  title: string;
  leadingIcon?: React.ElementType;
  children: ReactNode;
  id?: string;
}



// Helper component to render markdown content with tooltips
function MarkdownContent({ content }: { content: string }) {
  if (typeof content !== 'string') {
    console.error('MarkdownContent received non-string content:', content);
    return <p className="text-red-500">Error: Content is not a string.</p>;
  }

  return (
    <div className="prose prose-sm dark:prose-invert max-w-none text-gray-700 dark:text-gray-300 leading-relaxed">
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        components={{
          p: ({ node, children, ...props }) => (
            <p className="mb-4" {...props}>
              <InlineGlossaryText text={String(children)} />
            </p>
          ),
          strong: ({ node, children, ...props }) => (
            <strong className="font-semibold text-gray-900 dark:text-gray-100" {...props}>
              <InlineGlossaryText text={String(children)} />
            </strong>
          ),
          a: ({ node, children, ...props }) => (
            <a className="text-sky-600 dark:text-sky-400 hover:underline" {...props}>
              <InlineGlossaryText text={String(children)} />
            </a>
          ),
          li: ({ node, children, ...props }) => (
            <li {...props}>
              <InlineGlossaryText text={String(children)} />
            </li>
          ),
          h1: ({ node, children, ...props }) => (
            <h1 {...props}>
              <InlineGlossaryText text={String(children)} />
            </h1>
          ),
          h2: ({ node, children, ...props }) => (
            <h2 {...props}>
              <InlineGlossaryText text={String(children)} />
            </h2>
          ),
          h3: ({ node, children, ...props }) => (
            <h3 {...props}>
              <InlineGlossaryText text={String(children)} />
            </h3>
          ),
          h4: ({ node, children, ...props }) => (
            <h4 {...props}>
              <InlineGlossaryText text={String(children)} />
            </h4>
          ),
          h5: ({ node, children, ...props }) => (
            <h5 {...props}>
              <InlineGlossaryText text={String(children)} />
            </h5>
          ),
          h6: ({ node, children, ...props }) => (
            <h6 {...props}>
              <InlineGlossaryText text={String(children)} />
            </h6>
          ),
          // Handle code blocks without tooltip processing to avoid issues
          code: ({ node, children, ...props }) => (
            <code {...props}>{children}</code>
          ),
          pre: ({ node, children, ...props }) => (
            <pre {...props}>{children}</pre>
          ),
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
}

// New SectionCard component for individual sections with top gradient border
const SectionCard: React.FC<SectionCardProps> = ({ title, leadingIcon: LeadingIcon, children, id }) => {
  const cardRef = useRef<HTMLDivElement>(null);
  const iconRef = useRef<SVGSVGElement>(null);
  const trailingIconRef = useRef<SVGSVGElement>(null);
  const [iconStyle, setIconStyle] = useState({});
  const [trailingIconStyle, setTrailingIconStyle] = useState({});

  useEffect(() => {
    if (cardRef.current) {
      const cardRect = cardRef.current.getBoundingClientRect();
      if (cardRect.width <= 0) return;

      // Calculate style for the leading icon
      if (LeadingIcon && iconRef.current) {
        const iconRect = iconRef.current.getBoundingClientRect();
        const iconCenterX = iconRect.left + iconRect.width / 2;
        const cardStartX = cardRect.left;
        const relativeX = iconCenterX - cardStartX;
        let factor = relativeX / cardRect.width;
        factor = Math.max(0, Math.min(1, factor));
        const color = interpolateColor(GRADIENT_START_COLOR, GRADIENT_END_COLOR, factor);
        setIconStyle({ color });
      }

      // Calculate style for the trailing SparklesIcon
      if (trailingIconRef.current) {
        const trailingIconRect = trailingIconRef.current.getBoundingClientRect();
        const trailingIconCenterX = trailingIconRect.left + trailingIconRect.width / 2;
        const cardStartX = cardRect.left;
        const relativeXTrailing = trailingIconCenterX - cardStartX;
        let factorTrailing = relativeXTrailing / cardRect.width;
        factorTrailing = Math.max(0, Math.min(1, factorTrailing));
        const trailingColor = interpolateColor(GRADIENT_START_COLOR, GRADIENT_END_COLOR, factorTrailing);
        setTrailingIconStyle({ color: trailingColor });
      }
    }
  }, [LeadingIcon, title, children]);

  return (
    <div
      ref={cardRef}
      id={id}
      className="bg-white dark:bg-zinc-900 rounded-lg shadow-sm dark:shadow-zinc-800/30 border border-gray-200 dark:border-gray-700 border-t-0 overflow-hidden mb-8"
    >
      <div className="h-1 bg-gradient-to-r from-blue-500 to-purple-500"></div>
      <div className="p-6">
        {title && (
          <div className="flex items-center mb-6">
            {LeadingIcon && <LeadingIcon ref={iconRef} className={`h-7 w-7 mr-3 shrink-0`} style={iconStyle} />}
            <h2 className="text-2xl font-semibold text-gray-900 dark:text-white flex-grow">
              <InlineGlossaryText text={title} />
            </h2>
            <SubSparklesIcon ref={trailingIconRef} className="h-7 w-7 ml-2 shrink-0" style={trailingIconStyle} />
          </div>
        )}
        <div className="pl-10">{children}</div>
      </div>
    </div>
  );
};

// Helper to format score keys into readable titles
const formatScoreKey = (key: string): string => {
  if (key === 'total_weighted_score') return 'Total Weighted Score';
  return key
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

// Helper to format multi-level explanation keys into readable titles
const formatMultiLevelKey = (key: string): string => {
  switch (key) {
    case 'level_1_musician_friend':
      return 'For a Musician Friend (Level 1)';
    case 'level_2_juce_developer_no_ai_expert':
      return 'For a JUCE Developer (No AI Expertise) (Level 2)';
    case 'level_3_music_tech_researcher':
      return 'For a Music Technology Researcher (Level 3)';
    case 'level_4_ai_specialist_condensed':
      return 'For an AI Specialist (Condensed) (Level 4)';
    default:
      return formatScoreKey(key);
  }
};

const subCriteriaIconMap: Record<string, React.ElementType> = {
  // Implementation Readiness
  code_link_license: SubLinkIcon,
  build_snippet: SubCodeBracketIcon,
  environment_spec: SubComputerDesktopIcon,
  minimal_example: SubDocumentTextIcon,

  // Verified Performance Impact
  metric_table: SubTableCellsIcon,
  benchmarked_code_output: SubChartBarIcon,
  stat_sig_repetition: SubArrowPathIcon,

  // Problem Solving Novelty Insight
  conceptual_innovation: SubLightBulbIcon,
  problem_re_framing: SubAdjustmentsHorizontalIcon,
  clarity_of_explanation: SubChatBubbleLeftRightIcon,
  potential_for_unforeseen_applications: SubLightBulbIcon,

  // Audio Domain Translatability Impact
  direct_audio_application: SubMusicalNoteIcon,
  conceptual_audio_analogy: SubPuzzlePieceIcon,
  juce_cpp_integration_pathway: SubCommandLineIcon,
  workflow_enhancement_potential: SubArrowTrendingUpIcon,

  default: SubQuestionMarkCircleIcon,
};

// Gets icon based on the raw scoreName (JSON key)
const getSubCriterionIcon = (scoreName: string): React.ElementType => {
  return subCriteriaIconMap[scoreName] || subCriteriaIconMap.default;
};

// Icon map for main pillar titles in the Detailed Pillar Analysis section
const narrativePillarIconMap: Record<string, React.ElementType> = {
  clarity_and_presentation: ChatBubbleBottomCenterTextIcon, // Reusing an existing suitable icon
  methodology_and_execution: SubCogIcon, // Reusing SubCogIcon
  innovation_and_originality: MainLightBulbIcon,
  relevance_and_impact: SubArrowTrendingUpIcon,
  implementation_readiness: MainCommandLineIcon,
  verified_performance_impact: SubChartBarIcon,
  problem_solving_novelty_insight: SubPuzzlePieceIcon,
  audio_domain_translatability_impact: SubSpeakerWaveIcon,
  default: MainQuestionMarkCircleIcon, // Fallback icon for narrative pillars
};

// Gets icon for main narrative pillar titles
const getNarrativePillarIcon = (pillarKey: string): React.ElementType => {
  return narrativePillarIconMap[pillarKey.toLowerCase().replace(/\s+/g, '_')] || narrativePillarIconMap.default;
};

// Helper function to convert section titles to URL-friendly IDs
const titleToId = (title: string): string => {
  return title.toLowerCase().replace(/\s+/g, '-').replace(/[&:]/g, '');
};

const PaperEvaluationV3: React.FC<{ evaluationData: EvaluationDataV3; showRawMarkdown: boolean }> = ({ evaluationData, showRawMarkdown }) => {
  if (showRawMarkdown) {
    return (
      <SectionCard title="Raw Markdown" id={titleToId("Raw Markdown")} leadingIcon={MainDocumentTextIcon}>
        <pre className="whitespace-pre-wrap text-sm font-mono bg-gray-50 dark:bg-zinc-800 p-4 rounded-md overflow-x-auto">
          {JSON.stringify(evaluationData, null, 2)}
        </pre>
      </SectionCard>
    );
  }

  if (!evaluationData) {
    return <p>No evaluation data available.</p>;
  }

  const {
    metadata,
    executive_summary_for_audio_dev,
    scores,
    detailed_pillar_analysis,
    multi_level_explanation,
    brainstormed_audio_applications,
    key_learnings_for_audio_dev,
    critical_assessment_and_limitations,
    juce_implementation_sketch,
    methodological_deep_dive_adaptation,
    impact_on_my_research_and_development,
    final_verdict_for_audio_dev
  } = evaluationData;



  return (
    <div className="pb-8 font-roboto dark:bg-zinc-900">

      {executive_summary_for_audio_dev && (
        <SectionCard title="Executive Summary for Audio Developers" id={titleToId("Executive Summary for Audio Developers")} leadingIcon={ClipboardDocumentListIcon}>
          <div className="pl-0">
            <MarkdownContent content={executive_summary_for_audio_dev} />
          </div>
        </SectionCard>
      )}

      {/* Scores Section - Refactored */}
      {scores && (scores.total_weighted_score !== undefined || Object.values(scores).some(val => typeof val === 'object' && Object.keys(val).length > 0)) && (
         <SectionCard title="Quantitative Scores" id={titleToId("Quantitative Scores")} leadingIcon={MainScaleIcon}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 mb-6">
            {Object.entries(scores).map(([pillarKey, pillarValue]) => {
              if (pillarKey === 'total_weighted_score' || typeof pillarValue !== 'object') return null;
              const pillarScoresObj = pillarValue as { [key: string]: any };
              if (Object.keys(pillarScoresObj).length === 0) return null;

              // Separate 'total' from other scores for distinct layout
              const { total: pillarTotalScore, ...subCriteria } = pillarScoresObj;

              return (
                <div key={pillarKey} className="p-4 bg-white dark:bg-zinc-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 flex flex-col h-full">
                  {/* Section for title and sub-criteria - this part will grow */}
                  <div className="flex-grow">
                    <h3 className="text-lg font-semibold mb-3 pb-2 text-gray-700 dark:text-gray-200 capitalize">
                      <InlineGlossaryText text={formatScoreKey(pillarKey)} />
                    </h3>
                    <div className="space-y-2">
                      {Object.entries(subCriteria).map(([scoreName, scoreValue]) => {
                        const numericScore = typeof scoreValue === 'number' ? scoreValue : 0;
                        const scoreColor = interpolateColor(GRADIENT_START_COLOR, GRADIENT_END_COLOR, (numericScore / 100)); // Use 0-100 scale for color
                        const IconComponent = getSubCriterionIcon(scoreName);
                        return (
                          <div key={scoreName} className="text-sm">
                            <div className={`flex justify-between items-center`}>
                              <div className="flex items-center">
                                <IconComponent className="h-4 w-4 mr-1.5 text-gray-500 dark:text-gray-400 shrink-0" />
                                <span className={`text-gray-600 dark:text-gray-300`}>
                                  <InlineGlossaryText text={formatScoreKey(scoreName)} />:
                                </span>
                              </div>
                              <span className={`font-semibold text-gray-800 dark:text-gray-100`}>{numericScore}/100</span>
                            </div>
                            <div className="mt-1 h-2 w-full bg-gray-200 dark:bg-zinc-900 rounded-full overflow-hidden relative">
                              {/* Dotted line markers */}
                              {scoreMarkers.map(markerPos => (
                                <div
                                  key={`marker-${scoreName}-${markerPos}`}
                                  className="absolute top-0 bottom-0 border-l-2 border-dotted border-gray-500 dark:border-gray-500 z-20"
                                  style={{ left: `${markerPos}%` }}
                                ></div>
                              ))}
                              {/* Score fill */}
                              <div
                                className="h-full rounded-full relative z-10" // z-10 to ensure fill is above markers
                                style={{ width: `${numericScore}%`, backgroundColor: scoreColor }}
                              ></div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>

                  {/* Section for Total score - pushed to the bottom */}
                  {pillarTotalScore !== undefined && (
                    <div className="mt-auto border-t border-gray-200 dark:border-gray-600 pt-2 mt-4">
                      <div className="text-sm">
                        <div className="flex justify-between items-center">
                          <span className={`text-gray-600 dark:text-gray-300 font-bold`}>{formatScoreKey('total')}:</span>
                          <span className={`font-semibold text-gray-800 dark:text-gray-100 font-bold`}>{pillarTotalScore}/100</span>
                        </div>
                        <div className="mt-1 h-2 w-full bg-gray-200 dark:bg-zinc-900 rounded-full overflow-hidden relative">
                          {/* Dotted line markers */}
                          {scoreMarkers.map(markerPos => (
                            <div
                              key={`total-marker-${pillarKey}-${markerPos}`}
                              className="absolute top-0 bottom-0 border-l-2 border-dotted border-gray-500 dark:border-gray-500 z-20"
                              style={{ left: `${markerPos}%` }}
                            ></div>
                          ))}
                          {/* Score fill */}
                          {(() => {
                            const totalNumericScore = typeof pillarTotalScore === 'number' ? pillarTotalScore : 0;
                            const totalScoreColor = interpolateColor(GRADIENT_START_COLOR, GRADIENT_END_COLOR, (totalNumericScore / 100));
                            return (
                              <div
                                className="absolute top-0 left-0 h-full rounded-full z-10"
                                style={{ width: `${totalNumericScore}%`, backgroundColor: totalScoreColor }}
                              ></div>
                            );
                          })()}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
          {scores.total_weighted_score !== undefined && (
            <div className="mt-4 h-16 bg-gray-200 dark:bg-zinc-800 rounded-lg shadow-md relative overflow-hidden">
              {/* Dotted line markers for total weighted score */}
              {scoreMarkers.map(markerPos => (
                <div
                  key={`total-weighted-marker-${markerPos}`}
                  className="absolute top-0 bottom-0 border-l-2 border-dotted border-gray-500 dark:border-gray-500 z-15"
                  style={{ left: `${markerPos}%` }}
                ></div>
              ))}
              {/* Score fill */}
              <div
                className="absolute top-0 left-0 h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg z-10" // z-10 to ensure fill is above markers
                style={{ width: `${scores.total_weighted_score.toFixed(2)}%` }}
              ></div>
              {/* Text overlay - ensure it's above markers and fill */}
              <div className="absolute inset-0 flex flex-col items-center justify-center text-white z-20 p-2">
                <h3 className="text-xl font-bold">Total Weighted Score</h3>
                <p className="text-3xl font-extrabold">{scores.total_weighted_score.toFixed(2)}</p>
              </div>
            </div>
          )}
        </SectionCard>
      )}

      {detailed_pillar_analysis && Object.keys(detailed_pillar_analysis).length > 0 && (
        <div className="space-y-8">
          {Object.entries(detailed_pillar_analysis).map(([pillarKey, details]) => {
            const PillarIcon = getNarrativePillarIcon(pillarKey);
            const pillarTitle = formatScoreKey(pillarKey);

            return (
              <SectionCard key={pillarKey} title={pillarTitle} id={titleToId(pillarTitle)} leadingIcon={PillarIcon}>
                <div className="space-y-4">
                  {typeof details === 'object' && details !== null && Object.entries(details as PillarNarratives).map(([subPillarKey, subDetails]) => {
                    const SubPillarIcon = getSubCriterionIcon(subPillarKey);

                    const isDirectNarrative = typeof subDetails === 'string';
                    const isArrayNarrative = Array.isArray(subDetails);
                    const isNestedNarrative = typeof subDetails === 'object' && subDetails !== null && !Array.isArray(subDetails);

                    if (!subDetails || (isArrayNarrative && (subDetails as string[]).length === 0)) {
                      return null;
                    }

                    return (
                      <div key={subPillarKey} className="mb-3 last:mb-0">
                        <div className="flex items-center mb-1.5">
                          {SubPillarIcon && <SubPillarIcon className="h-5 w-5 mr-1.5 text-blue-600 dark:text-blue-400 shrink-0" />}
                          <h4 className="text-lg font-medium text-gray-700 dark:text-gray-200 capitalize">
                            <InlineGlossaryText text={formatScoreKey(subPillarKey)} />
                          </h4>
                        </div>
                        {isDirectNarrative && (
                          <div className="ml-7">
                            <MarkdownContent content={subDetails as string} />
                          </div>
                        )}
                        {isArrayNarrative && (subDetails as string[]).length > 0 && (
                          <ul className="list-disc list-outside ml-11 space-y-1">
                            {(subDetails as string[]).map((item, index) => (
                              item && <li key={index} className="text-gray-700 dark:text-gray-300"><MarkdownContent content={item} /></li>
                            ))}
                          </ul>
                        )}
                        {isNestedNarrative && (
                           <div className="ml-7 space-y-2">
                            {Object.entries(subDetails as Record<string, string>).map(([itemKey, itemValue]) => (
                              itemValue && <div key={itemKey}>
                                <h5 className="text-md font-semibold text-gray-600 dark:text-gray-300 capitalize">{formatScoreKey(itemKey)}:</h5>
                                <MarkdownContent content={itemValue} />
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </SectionCard>
            );
          })}
        </div>
      )}

      {multi_level_explanation && Object.values(multi_level_explanation).some(val => !!val) && (
        <SectionCard title="Multi-Level Explanations" id={titleToId("Multi-Level Explanations")} leadingIcon={AcademicCapIcon}>
          <div className="space-y-6">
            {Object.entries(multi_level_explanation).map(([levelKey, explanation]) => (
              explanation && <div key={levelKey} className="">
                <div className="flex items-center mb-2">
                  <ChatBubbleBottomCenterTextIcon className="h-6 w-6 mr-2 text-blue-600 dark:text-blue-400 shrink-0" />
                  <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-100">
                    <InlineGlossaryText text={formatMultiLevelKey(levelKey)} />
                  </h3>
                </div>
                <div>
                  <MarkdownContent content={typeof explanation === 'string' ? explanation : ''} />
                </div>
              </div>
            ))}
          </div>
        </SectionCard>
      )}

      {brainstormed_audio_applications && brainstormed_audio_applications.length > 0 && brainstormed_audio_applications.some(app => app.application_idea) && (
        <SectionCard title="Brainstormed Audio Applications" id={titleToId("Brainstormed Audio Applications")} leadingIcon={MainLightBulbIcon}>
          <div className="space-y-6">
            {brainstormed_audio_applications.map((app, index) => (
              app.application_idea && <div key={index} className="p-4 bg-gray-50 dark:bg-zinc-700/70 rounded-md shadow-sm">
                <h3 className="text-xl font-semibold mb-2 pb-2 border-b border-blue-200 dark:border-blue-600 text-blue-600 dark:text-blue-300">
                  <InlineGlossaryText text={app.application_idea} />
                </h3>
                <div className="pl-6">
                  <MarkdownContent content={typeof app.brief_description === 'string' ? app.brief_description : ''} />
                </div>
              </div>
            ))}
          </div>
        </SectionCard>
      )}

      {key_learnings_for_audio_dev && key_learnings_for_audio_dev.length > 0 && key_learnings_for_audio_dev.some(l => !!l) && (
        <SectionCard title="Key Learnings for Audio Developers" id={titleToId("Key Learnings for Audio Developers")} leadingIcon={KeyIcon}>
          {/* Removed pl-7 to align the border with SectionCard's pl-10 */}
          <ul className="list-disc list-outside pr-2 py-2 space-y-2">
            {key_learnings_for_audio_dev.map((learning, index) => (
              learning && <li key={index} className="text-gray-700 dark:text-gray-300 ml-4"> {/* Added ml-4 to indent text from border */}
                <MarkdownContent content={typeof learning === 'string' ? learning : ''} />
              </li>
            ))}
          </ul>
        </SectionCard>
      )}

      {critical_assessment_and_limitations && (
        <SectionCard title="Critical Assessment and Limitations" id={titleToId("Critical Assessment and Limitations")} leadingIcon={MainExclamationTriangleIcon}>
          <div className="pl-0">
            <MarkdownContent content={critical_assessment_and_limitations} />
          </div>
        </SectionCard>
      )}

      {juce_implementation_sketch && (juce_implementation_sketch.description || juce_implementation_sketch.code_sketch) && (
        <SectionCard title="JUCE Implementation Sketch" id={titleToId("JUCE Implementation Sketch")} leadingIcon={CodeBracketSquareIcon}>
          {juce_implementation_sketch.description && (
            <div className="mb-4">
              <h4 className="text-lg font-semibold text-purple-600 dark:text-purple-400 mb-2">Conceptual Outline:</h4>
              <div className="pl-6">
                <MarkdownContent content={juce_implementation_sketch.description} />
              </div>
            </div>
          )}
          {juce_implementation_sketch.code_sketch && (
            <div>
              <h4 className="text-lg font-semibold text-purple-600 dark:text-purple-400 mb-2">Code Sketch (Pseudocode/C++):</h4>
              {showRawMarkdown ? (
                <pre className="p-4 bg-gray-800 dark:bg-black text-white dark:text-gray-200 rounded-md shadow-inner overflow-x-auto text-sm font-mono whitespace-pre-wrap break-all">
                  <code>{juce_implementation_sketch.code_sketch}</code>
                </pre>
              ) : (
                <div className="pl-6">
                  <div className="p-4 bg-gray-800 dark:bg-black rounded-md shadow-inner overflow-x-auto">
                    <MarkdownContent content={`\`\`\`cpp\n${juce_implementation_sketch.code_sketch}\n\`\`\``} />
                  </div>
                </div>
              )}
            </div>
          )}
        </SectionCard>
      )}

      {methodological_deep_dive_adaptation && methodological_deep_dive_adaptation.length > 0 && methodological_deep_dive_adaptation.some(item => item.methodName) && (
        <SectionCard title="Methodological Deep Dive & Adaptation" id={titleToId("Methodological Deep Dive & Adaptation")} leadingIcon={MainCubeTransparentIcon}>
          <div className="space-y-8"> {/* Retain space-y for overall spacing */}
            {methodological_deep_dive_adaptation.map((item, index) => (
              item.methodName && <div key={index} className="py-4"> {/* Removed card styling, added py-4 for vertical separation */}
                <h3 className="text-xl font-semibold mb-3 pb-2 border-b border-gray-300 dark:border-gray-600 text-gray-800 dark:text-gray-100">
                  <InlineGlossaryText text={typeof item.methodName === 'string' ? item.methodName : 'Unnamed Method'} />
                </h3>
                <div className="space-y-4 mt-3 pl-0"> {/* Adjusted padding/margin */}
                  {item.simplifiedExplanationForAudioDev && (
                    <div>
                      <div className="flex items-center mb-1">
                        <SubLightBulbIcon className="h-5 w-5 mr-2 text-blue-600 dark:text-blue-400 shrink-0" />
                        <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-100">Simplified Explanation:</h4>
                      </div>
                      <div className="pl-7"> {/* Indent content under icon+title */}
                        <MarkdownContent content={typeof item.simplifiedExplanationForAudioDev === 'string' ? item.simplifiedExplanationForAudioDev : ''} />
                      </div>
                    </div>
                  )}
                  {item.prerequisites_for_audio_adaptation && item.prerequisites_for_audio_adaptation.length > 0 && item.prerequisites_for_audio_adaptation.some(p => !!p) && (
                    <div>
                      <div className="flex items-center mb-1">
                        <ClipboardDocumentListIcon className="h-5 w-5 mr-2 text-blue-600 dark:text-blue-400 shrink-0" />
                        <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-100">Prerequisites for Audio Adaptation:</h4>
                      </div>
                      <ul className="list-disc list-outside pl-7 pr-2 py-2 space-y-1"> {/* Reduced pl from 11 to 7 */}
                        {item.prerequisites_for_audio_adaptation.map((prerequisite, pIndex) => (
                          prerequisite && <li key={pIndex} className="text-gray-700 dark:text-gray-300 ml-0"> {/* Removed ml-4, list-outside handles indent */}
                            <MarkdownContent content={typeof prerequisite === 'string' ? prerequisite : ''} />
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                  {item.stepByStepAdaptationGuide_conceptual && item.stepByStepAdaptationGuide_conceptual.length > 0 && item.stepByStepAdaptationGuide_conceptual.some(s => !!s) && (
                    <div>
                      <div className="flex items-center mb-1">
                        <ListBulletIcon className="h-5 w-5 mr-2 text-blue-600 dark:text-blue-400 shrink-0" />
                        <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-100">Step-by-Step Conceptual Adaptation Guide:</h4>
                      </div>
                      <ul className="list-disc list-outside pl-7 pr-2 py-2 space-y-1"> {/* Reduced pl from 11 to 7 */}
                        {item.stepByStepAdaptationGuide_conceptual.map((step, sIndex) => (
                          step && <li key={sIndex} className="text-gray-700 dark:text-gray-300 ml-0"> {/* Removed ml-4 */}
                            <MarkdownContent content={typeof step === 'string' ? step : ''} />
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                  {item.practicalAudioExample && (item.practicalAudioExample.scenarioDescription || item.practicalAudioExample.how_method_might_apply || item.practicalAudioExample.expected_audio_outcome_if_successful) && (
                    <div>
                      <div className="flex items-center mb-1">
                        <BeakerIcon className="h-5 w-5 mr-2 text-blue-600 dark:text-blue-400 shrink-0" />
                        <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-100">Practical Audio Example:</h4>
                      </div>
                      <div className="pl-7"> {/* Indent content under icon+title */}
                        {item.practicalAudioExample.scenarioDescription && (
                          <p className="italic text-gray-600 dark:text-gray-400 mb-1">
                            Scenario: <InlineGlossaryText text={typeof item.practicalAudioExample.scenarioDescription === 'string' ? item.practicalAudioExample.scenarioDescription : ''} />
                          </p>
                        )}
                        {item.practicalAudioExample.how_method_might_apply && (
                          <div className="mb-1">
                            <strong className="text-gray-700 dark:text-gray-200">Application:</strong> <MarkdownContent content={typeof item.practicalAudioExample.how_method_might_apply === 'string' ? item.practicalAudioExample.how_method_might_apply : ''} />
                          </div>
                        )}
                        {item.practicalAudioExample.expected_audio_outcome_if_successful && (
                          <div>
                            <strong className="text-gray-700 dark:text-gray-200">Expected Outcome:</strong> <MarkdownContent content={typeof item.practicalAudioExample.expected_audio_outcome_if_successful === 'string' ? item.practicalAudioExample.expected_audio_outcome_if_successful : ''} />
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </SectionCard>
      )}

      {impact_on_my_research_and_development && Object.values(impact_on_my_research_and_development).some(val => !!val || (Array.isArray(val) && val.length > 0 && val.some(q => !!q))) && (
        <SectionCard title="Impact on My Research & Development" id={titleToId("Impact on My Research & Development")} leadingIcon={MainChatBubbleLeftRightIcon}>
          <div className="space-y-6">
            {impact_on_my_research_and_development.new_ideas_sparked && (
              <div>
                <div className="flex items-center mb-1">
                  <SubLightBulbIcon className="h-5 w-5 mr-2 text-blue-600 dark:text-blue-400 shrink-0" />
                  <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-100">New Ideas Sparked:</h4>
                </div>
                <div className="pl-7">
                  <MarkdownContent content={typeof impact_on_my_research_and_development.new_ideas_sparked === 'string' ? impact_on_my_research_and_development.new_ideas_sparked : ''} />
                </div>
              </div>
            )}
            {impact_on_my_research_and_development.refinement_of_existing_ideas && (
              <div>
                <div className="flex items-center mb-1">
                  <SubAdjustmentsHorizontalIcon className="h-5 w-5 mr-2 text-blue-600 dark:text-blue-400 shrink-0" />
                  <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-100">Refinement of Existing Ideas:</h4>
                </div>
                <div className="pl-7">
                  <MarkdownContent content={typeof impact_on_my_research_and_development.refinement_of_existing_ideas === 'string' ? impact_on_my_research_and_development.refinement_of_existing_ideas : ''} />
                </div>
              </div>
            )}
            {impact_on_my_research_and_development.potential_thesis_contribution_angle && (
              <div>
                <div className="flex items-center mb-1">
                  <SubArrowTrendingUpIcon className="h-5 w-5 mr-2 text-blue-600 dark:text-blue-400 shrink-0" />
                  <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-100">Potential Thesis Contribution Angle:</h4>
                </div>
                <div className="pl-7">
                  <MarkdownContent content={typeof impact_on_my_research_and_development.potential_thesis_contribution_angle === 'string' ? impact_on_my_research_and_development.potential_thesis_contribution_angle : ''} />
                </div>
              </div>
            )}
            {impact_on_my_research_and_development.questions_for_further_investigation && impact_on_my_research_and_development.questions_for_further_investigation.length > 0 && impact_on_my_research_and_development.questions_for_further_investigation.some(q => !!q) && (
              <div>
                <div className="flex items-center mb-1">
                  <SubQuestionMarkCircleIcon className="h-5 w-5 mr-2 text-blue-600 dark:text-blue-400 shrink-0" />
                  <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-100">Questions for Further Investigation:</h4>
                </div>
                <ul className="list-disc list-outside pl-7 pr-2 py-2 space-y-1">
                  {impact_on_my_research_and_development.questions_for_further_investigation.map((question, qIndex) => (
                    question && <li key={qIndex} className="text-gray-700 dark:text-gray-300">
                      <MarkdownContent content={typeof question === 'string' ? question : ''} />
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </SectionCard>
      )}

      {final_verdict_for_audio_dev && (
        final_verdict_for_audio_dev.is_worth_reading_thoroughly ||
        final_verdict_for_audio_dev.primary_value_proposition ||
        final_verdict_for_audio_dev.overall_suitability_score_for_my_needs ||
        final_verdict_for_audio_dev.concluding_remarks
      ) && (
        <SectionCard title="Final Verdict for Audio Developers" id={titleToId("Final Verdict for Audio Developers")} leadingIcon={CheckCircleIcon}>
          {final_verdict_for_audio_dev.is_worth_reading_thoroughly && (
            <div className="mb-4">
              <div className="flex items-center mb-1">
                <SubBookOpenIcon className="h-5 w-5 mr-2 text-blue-600 dark:text-blue-400 shrink-0" />
                <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-100">Is it Worth Reading Thoroughly?</h4>
              </div>
              <div className="pl-7">
                <MarkdownContent content={typeof final_verdict_for_audio_dev.is_worth_reading_thoroughly === 'string' ? final_verdict_for_audio_dev.is_worth_reading_thoroughly : ''} />
              </div>
            </div>
          )}
          {final_verdict_for_audio_dev.primary_value_proposition && (
            <div className="mb-4">
              <div className="flex items-center mb-1">
                <SubLightBulbIcon className="h-5 w-5 mr-2 text-blue-600 dark:text-blue-400 shrink-0" />
                <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-100">Primary Value Proposition:</h4>
              </div>
              <div className="pl-7">
                <MarkdownContent content={typeof final_verdict_for_audio_dev.primary_value_proposition === 'string' ? final_verdict_for_audio_dev.primary_value_proposition : ''} />
              </div>
            </div>
          )}
          {final_verdict_for_audio_dev.overall_suitability_score_for_my_needs && (
            <div className="mb-4">
              <div className="flex items-center mb-1">
                <SubScaleIcon className="h-5 w-5 mr-2 text-blue-600 dark:text-blue-400 shrink-0" />
                <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-100">Overall Suitability Score for My Needs:</h4>
              </div>
              <div className="pl-7">
                <MarkdownContent content={typeof final_verdict_for_audio_dev.overall_suitability_score_for_my_needs === 'string' ? final_verdict_for_audio_dev.overall_suitability_score_for_my_needs : ''} />
              </div>
            </div>
          )}
          {final_verdict_for_audio_dev.concluding_remarks && (
            <div className="mb-4"> {/* Changed from just <div> to <div className="mb-4"> for consistency */}
              <div className="flex items-center mb-1">
                <SubDocumentTextIcon className="h-5 w-5 mr-2 text-blue-600 dark:text-blue-400 shrink-0" />
                <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-100">Concluding Remarks:</h4>
              </div>
              <div className="pl-7">
                <MarkdownContent content={typeof final_verdict_for_audio_dev.concluding_remarks === 'string' ? final_verdict_for_audio_dev.concluding_remarks : ''} />
              </div>
            </div>
          )}
        </SectionCard>
      )}

    </div>
  );
};

export default PaperEvaluationV3;
