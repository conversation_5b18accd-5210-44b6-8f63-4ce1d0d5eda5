'use client';

import React, { useMemo } from 'react';
import { useGlossary } from '@/context/GlossaryContext';
import Tooltip from '@/components/Tooltip';

interface InlineGlossaryTextProps {
  text: string;
  className?: string;
}

/**
 * A component that adds tooltips to glossary terms while maintaining inline text flow.
 * Unlike GlossaryText, this component doesn't use ReactMarkdown to avoid block-level elements.
 */
export default function InlineGlossaryText({ text, className = '' }: InlineGlossaryTextProps) {
  const { terms, isLoading } = useGlossary();

  // Process the content to add tooltips to terms
  const processedContent = useMemo(() => {
    if (isLoading || !terms || terms.length === 0 || !text) {
      return [{ type: 'text', content: text }];
    }
    
    // Create a map of terms for faster lookup
    const termMap = new Map<string, { display: string; definition: string }>();
    terms.forEach(term => {
      // Skip very short terms to avoid false positives
      if (term.term.length >= 3) {
        termMap.set(term.term.toLowerCase(), {
          display: term.term,
          definition: `${term.term}${term.acronym ? ` (${term.acronym})` : ''}: ${term.definition}`
        });
      }
      
      if (term.acronym && term.acronym.length >= 2) {
        termMap.set(term.acronym.toLowerCase(), {
          display: term.acronym,
          definition: `${term.term}${term.acronym ? ` (${term.acronym})` : ''}: ${term.definition}`
        });
      }
    });
    
    // Sort terms by length (longest first) to handle nested terms
    const sortedTerms = Array.from(termMap.keys()).sort((a, b) => b.length - a.length);
    
    let processed = text;
    
    // Process each term and wrap in a tooltip marker
    sortedTerms.forEach(term => {
      const termInfo = termMap.get(term);
      if (!termInfo) return;
      
      // Create a regex that matches the term as a whole word
      const regex = new RegExp(`\\b(${term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})\\b`, 'gi');
      
      // Replace each occurrence with a tooltip marker
      processed = processed.replace(regex, (match) => {
        // Don't process if already inside a tooltip to avoid nesting
        if (match.includes('{{GLOSSARY:')) return match;
        return `{{GLOSSARY:${match}:${encodeURIComponent(termInfo.definition)}}}`;
      });
    });
    
    // Parse the processed content into segments
    const segments: Array<{ type: 'text' | 'tooltip'; content: string; definition?: string }> = [];
    const markerRegex = /\{\{GLOSSARY:(.*?):(.*?)\}\}/g;
    let lastIndex = 0;
    let match;
    
    while ((match = markerRegex.exec(processed)) !== null) {
      // Add text before the marker
      if (match.index > lastIndex) {
        const beforeText = processed.substring(lastIndex, match.index);
        if (beforeText) {
          segments.push({ type: 'text', content: beforeText });
        }
      }
      
      // Add the tooltip segment
      const term = match[1];
      const definition = decodeURIComponent(match[2]);
      segments.push({ type: 'tooltip', content: term, definition });
      
      lastIndex = match.index + match[0].length;
    }
    
    // Add any remaining text
    if (lastIndex < processed.length) {
      const remainingText = processed.substring(lastIndex);
      if (remainingText) {
        segments.push({ type: 'text', content: remainingText });
      }
    }
    
    // If no segments were created, return the original text
    if (segments.length === 0) {
      segments.push({ type: 'text', content: text });
    }
    
    return segments;
  }, [text, terms, isLoading]);

  // Render the processed content
  return (
    <span className={className}>
      {processedContent.map((segment, index) => {
        if (segment.type === 'tooltip' && segment.definition) {
          return (
            <Tooltip key={`tooltip-${index}`} text={segment.definition}>
              <span className="border-b border-dotted border-gray-400 dark:border-gray-600 cursor-help">
                {segment.content}
              </span>
            </Tooltip>
          );
        } else {
          return (
            <span key={`text-${index}`}>
              {segment.content}
            </span>
          );
        }
      })}
    </span>
  );
}
